"use client";

import { useState, useEffect } from "react";
import { LoginForm } from "@/components/auth/login-form";
import { Sidebar } from "@/components/ui/sidebar";
import { StatsCards } from "@/components/dashboard/stats-cards";
import { RecentOrders } from "@/components/dashboard/recent-orders";
import { SalesChart } from "@/components/dashboard/sales-chart";
import { ProductTable } from "@/components/products/product-table";
import { AddProductForm } from "@/components/products/add-product-form";
import { EditProductForm } from "@/components/products/edit-product-form";
import { CategoryTable } from "@/components/categories/category-table";
import { AddCategoryForm } from "@/components/categories/add-category-form";
import { EditCategoryForm } from "@/components/categories/edit-category-form";
import { OrderTable } from "@/components/orders/order-table";
import { CustomerTable } from "@/components/customers/customer-table";
import { InventoryTable } from "@/components/inventory/inventory-table";
import { authService } from "@/lib/services";
import { DollarSign, ShoppingCart, Users, BarChart3 } from "lucide-react";
import { Toaster } from "sonner";
import { SettingsPanel } from "@/components/settings/settings-panel";
import { AnalyticsDashboard } from "@/components/analytics/analytics-dashboard";
import { CouponTable } from "@/components/coupons/coupon-table";

export default function AdminPanel() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState("dashboard");
  const [showAddProduct, setShowAddProduct] = useState(false);
  const [showEditProduct, setShowEditProduct] = useState(false);
  const [editingProduct, setEditingProduct] = useState<any>(null);
  const [showAddCategory, setShowAddCategory] = useState(false);
  const [showEditCategory, setShowEditCategory] = useState(false);
  const [editingCategory, setEditingCategory] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is already logged in
    const checkAuth = async () => {
      try {
        const isValid = await authService.checkAuth();
        if (isValid) {
          const profile = await authService.getProfile();
          if (profile.user.role === 'admin') {
            setIsAuthenticated(true);
            setCurrentUser(profile.user);
          } else {
            authService.logout();
          }
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        authService.logout();
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  const handleLogin = (user: any) => {
    setIsAuthenticated(true);
    setCurrentUser(user);
  };

  const handleLogout = () => {
    authService.logout();
    setIsAuthenticated(false);
    setCurrentUser(null);
    setCurrentPage("dashboard");
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <LoginForm onLogin={handleLogin} />;
  }

  const handleNavigate = (page: string) => {
    setCurrentPage(page);
    setShowAddProduct(false);
    setShowEditProduct(false);
    setEditingProduct(null);
    setShowAddCategory(false);
    setShowEditCategory(false);
    setEditingCategory(null);
  };

  const handleProductAdded = () => {
    setShowAddProduct(false);
    // Refresh products if on products page
    if (currentPage === 'products') {
      window.location.reload();
    }
  };

  const handleEditProduct = (product: any) => {
    setEditingProduct(product);
    setShowEditProduct(true);
  };

  const handleProductUpdated = () => {
    setShowEditProduct(false);
    setEditingProduct(null);
    // Refresh products if on products page
    if (currentPage === 'products') {
      window.location.reload();
    }
  };

  const handleCategoryAdded = () => {
    setShowAddCategory(false);
    // Refresh categories if on categories page
    if (currentPage === 'categories') {
      window.location.reload();
    }
  };

  const handleEditCategory = (category: any) => {
    setEditingCategory(category);
    setShowEditCategory(true);
  };

  const handleCategoryUpdated = () => {
    setShowEditCategory(false);
    setEditingCategory(null);
    // Refresh categories if on categories page
    if (currentPage === 'categories') {
      window.location.reload();
    }
  };

  const renderContent = () => {
    switch (currentPage) {
      case "dashboard":
        return (
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold">Dashboard</h1>
              <p className="text-muted-foreground">Welcome back! Here's what's happening with your store.</p>
            </div>
            <StatsCards />
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <SalesChart />
              <RecentOrders />
            </div>
          </div>
        );
      case "products":
        return (
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold">Products</h1>
              <p className="text-muted-foreground">Manage your product catalog and inventory.</p>
            </div>
            <ProductTable
              onAddProduct={() => setShowAddProduct(true)}
              onEditProduct={handleEditProduct}
            />
          </div>
        );
      case "categories":
        return (
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold">Categories</h1>
              <p className="text-muted-foreground">Organize your products with categories.</p>
            </div>
            <CategoryTable
              onAddCategory={() => setShowAddCategory(true)}
              onEditCategory={handleEditCategory}
            />
          </div>
        );
      case "orders":
        return (
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold">Orders</h1>
              <p className="text-muted-foreground">Track and manage customer orders.</p>
            </div>
            <OrderTable />
          </div>
        );
      case "customers":
        return (
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold">Customers</h1>
              <p className="text-muted-foreground">Manage your customer base and relationships.</p>
            </div>
            <CustomerTable />
          </div>
        );
      case "coupons":
        return <CouponTable />;
      case "inventory":
        return (
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold">Inventory</h1>
              <p className="text-muted-foreground">Track stock levels and manage inventory.</p>
            </div>
            <InventoryTable />
          </div>
        );
      case "analytics":
        return <AnalyticsDashboard />;
      case "settings":
        return <SettingsPanel />;
      default:
        return null;
    }
  };

  return (
    <div className="flex h-screen bg-slate-50">
      <Sidebar
        currentPage={currentPage}
        onNavigate={handleNavigate}
        onLogout={handleLogout}
        currentUser={currentUser}
      />
      <main className="flex-1 overflow-auto">
        <div className="container mx-auto p-6 md:ml-4">
          {renderContent()}
        </div>
      </main>

      {/* Add Product Modal */}
      {showAddProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <AddProductForm
              onClose={() => setShowAddProduct(false)}
              onProductAdded={handleProductAdded}
            />
          </div>
        </div>
      )}

      {/* Edit Product Modal */}
      {showEditProduct && editingProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <EditProductForm
              product={editingProduct}
              onClose={() => {
                setShowEditProduct(false);
                setEditingProduct(null);
              }}
              onProductUpdated={handleProductUpdated}
            />
          </div>
        </div>
      )}

      {/* Add Category Modal */}
      {showAddCategory && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <AddCategoryForm
              onClose={() => setShowAddCategory(false)}
              onCategoryAdded={handleCategoryAdded}
            />
          </div>
        </div>
      )}

      {/* Edit Category Modal */}
      {showEditCategory && editingCategory && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <EditCategoryForm
              category={editingCategory}
              onClose={() => {
                setShowEditCategory(false);
                setEditingCategory(null);
              }}
              onCategoryUpdated={handleCategoryUpdated}
            />
          </div>
        </div>
      )}

      <Toaster position="top-right" />
    </div>
  );
}